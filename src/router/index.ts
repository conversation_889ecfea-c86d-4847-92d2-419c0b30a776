
import { createRouter, createWebHistory } from 'vue-router';
import DashboardLayout from '../views/dashboard/index.vue';
import { isAuthenticated } from '../store/auth';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue'),
  },
  {
    path: '/',
    component: DashboardLayout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: { template: '<div class="p-4">驾驶舱内容</div>' }
      },
      {
        path: 'my-apps',
        name: 'MyApps',
        redirect: '/my-apps/product-collection',
        children: [
          {
            path: 'product-collection',
            name: 'ProductCollection',
            component: () => import('../views/my-apps/product-collection/index.vue')
          },
          {
            path: 'smart-crop',
            name: 'SmartCrop',
            component: { template: '<div class="p-4">智能裁图内容</div>' }
          },
          {
            path: 'one-click-cutout',
            name: 'OneClickCutout',
            component: { template: '<div class="p-4">一键抠图内容</div>' }
          },
          {
            path: 'super-split',
            name: 'SuperSplit',
            component: { template: '<div class="p-4">超级裂变内容</div>' }
          },
          {
            path: 'title-generator',
            name: 'TitleGenerator',
            component: { template: '<div class="p-4">标题生成内容</div>' }
          },
          {
            path: 'batch-listing',
            name: 'BatchListing',
            component: { template: '<div class="p-4">批量刊登内容</div>' }
          }
        ]
      },
      {
        path: 'workflows',
        name: 'Workflows',
        component: { template: '<div class="p-4">工作流内容</div>' }
      },
      {
        path: 'gallery',
        name: 'Gallery',
        component: { template: '<div class="p-4">图库管理内容</div>' }
      },
      {
        path: 'products',
        name: 'Products',
        component: { template: '<div class="p-4">商品管理内容</div>' }
      },
      {
        path: 'app-market',
        name: 'AppMarket',
        component: { template: '<div class="p-4">应用市场内容</div>' }
      },
      {
        path: 'account-settings',
        name: 'AccountSettings',
        component: { template: '<div class="p-4">账号设置内容</div>' }
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach((to, _from, next) => {
  if (to.matched.some(record => record.meta.requiresAuth) && !isAuthenticated.value) {
    next({ name: 'Login' });
  } else if (to.name === 'Login' && isAuthenticated.value) {
    next({ name: 'Dashboard' });
  } else {
    next();
  }
});

export default router;
