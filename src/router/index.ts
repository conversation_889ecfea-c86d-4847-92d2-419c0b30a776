
import { createRouter, createWebHistory } from 'vue-router';
import DashboardLayout from '../views/dashboard/index.vue';
import { isAuthenticated } from '../store/auth';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue'),
  },
  {
    path: '/',
    component: DashboardLayout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/dashboard.vue')
      },
      {
        path: 'my-apps',
        name: 'MyApps',
        component: { template: '<router-view />' },
        redirect: '/my-apps/product-collection',
        children: [
          {
            path: 'product-collection',
            name: 'ProductCollection',
            component: () => import('../views/my-apps/product-collection/index.vue')
          },
          {
            path: 'smart-crop',
            name: 'SmartCrop',
            component: () => import('../views/my-apps/smart-crop/index.vue')
          },
          {
            path: 'one-click-cutout',
            name: 'OneClickCutout',
            component: () => import('../views/my-apps/one-click-cutout/index.vue')
          },
          {
            path: 'super-split',
            name: 'SuperSplit',
            component: () => import('../views/my-apps/super-split/index.vue')
          },
          {
            path: 'title-generator',
            name: 'TitleGenerator',
            component: () => import('../views/my-apps/title-generator/index.vue')
          },
          {
            path: 'batch-listing',
            name: 'BatchListing',
            component: () => import('../views/my-apps/batch-listing/index.vue')
          }
        ]
      },
      {
        path: 'workflows',
        name: 'Workflows',
        component: () => import('../views/workflows/index.vue')
      },
      {
        path: 'gallery',
        name: 'Gallery',
        component: () => import('../views/gallery/index.vue')
      },
      {
        path: 'products',
        name: 'Products',
        component: () => import('../views/products/index.vue')
      },
      {
        path: 'app-market',
        name: 'AppMarket',
        component: () => import('../views/app-market/index.vue')
      },
      {
        path: 'account-settings',
        name: 'AccountSettings',
        component: () => import('../views/account-settings/index.vue')
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach((to, _from, next) => {
  if (to.matched.some(record => record.meta.requiresAuth) && !isAuthenticated.value) {
    next({ name: 'Login' });
  } else if (to.name === 'Login' && isAuthenticated.value) {
    next({ name: 'Dashboard' });
  } else {
    next();
  }
});

export default router;
