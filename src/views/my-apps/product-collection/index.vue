<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">商品采集</h1>
      <p class="mt-1 text-sm text-gray-600">管理和查看您的商品采集任务</p>
    </div>

    <!-- 头部操作区域 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex space-x-4">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon class="mr-1"><Plus /></el-icon>
          新建采集
        </el-button>
        <el-button @click="exportTable">
          <el-icon class="mr-1"><Download /></el-icon>
          导出表格
        </el-button>
      </div>
      <div>
        <el-button type="info" @click="downloadPlugin">
          <el-icon class="mr-1"><Download /></el-icon>
          下载采集插件
        </el-button>
      </div>
    </div>

    <!-- 采集列表 -->
    <div class="bg-white rounded-lg shadow">
      <el-table :data="collectionList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="采集ID" width="120" />
        <el-table-column label="采集类型" width="200">
          <template #default="scope">
            <div class="text-sm">
              <div class="font-medium">类型：{{ scope.row.type }}</div>
              <div class="text-gray-500">平台：{{ scope.row.platform }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="采集数量" width="150">
          <template #default="scope">
            <div class="text-sm">
              <div>目标：{{ scope.row.targetCount }}</div>
              <div class="text-green-600">成功：{{ scope.row.successCount }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="采集状态" width="150">
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)" 
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
            <div v-if="scope.row.status.includes('失败')" class="mt-1">
              <el-button type="text" size="small" @click="viewFailureReason(scope.row)">
                查看原因
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="collector" label="采集人" width="100" />
        <el-table-column prop="createTime" label="采集时间" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewDetails(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-center py-4">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新建采集弹窗 -->
    <CreateCollectionDialog 
      v-model="showCreateDialog" 
      @success="handleCreateSuccess"
    />

    <!-- 查看详情弹窗 -->
    <ViewDetailsDialog 
      v-model="showDetailsDialog" 
      :collection-data="selectedCollection"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import CreateCollectionDialog from './components/CreateCollectionDialog.vue';
import ViewDetailsDialog from './components/ViewDetailsDialog.vue';
import {
  collectionStore,
  getCollectionTasks,
  exportCollectionData,
  downloadPlugin as downloadPluginApi
} from '../../../store/product-collection';

// 响应式数据
const showCreateDialog = ref(false);
const showDetailsDialog = ref(false);
const selectedCollection = ref(null);
const collectionList = ref([]);

// 使用store中的状态
const { loading, pagination } = collectionStore;

// 方法
const getStatusType = (status: string) => {
  if (status === '已完成') return 'success';
  if (status === '进行中') return 'warning';
  if (status.includes('失败')) return 'danger';
  return 'info';
};

const exportTable = async () => {
  try {
    await exportCollectionData('excel');
    ElMessage.success('表格导出成功！');
  } catch (error) {
    ElMessage.error('导出失败，请重试');
  }
};

const downloadPlugin = async () => {
  try {
    await downloadPluginApi();
    ElMessage.success('插件下载成功！');
  } catch (error) {
    ElMessage.error('下载失败，请重试');
  }
};

const viewFailureReason = (row: any) => {
  const reason = row.failureReason || '网络超时导致部分商品采集失败';
  ElMessage.info(`采集ID ${row.id} 的失败原因：${reason}`);
};

const viewDetails = (row: any) => {
  selectedCollection.value = row;
  showDetailsDialog.value = true;
};

const handleCreateSuccess = () => {
  // 刷新列表
  loadCollectionList();
  ElMessage.success('采集任务创建成功！');
};

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  loadCollectionList();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadCollectionList();
};

const loadCollectionList = async () => {
  try {
    const { data, total } = await getCollectionTasks(pagination.currentPage, pagination.pageSize);
    collectionList.value = data;
    pagination.total = total;
  } catch (error) {
    ElMessage.error('加载数据失败，请重试');
  }
};

onMounted(() => {
  loadCollectionList();
});
</script>
