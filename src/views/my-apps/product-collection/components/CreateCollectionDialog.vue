<template>
  <el-dialog
    v-model="dialogVisible"
    title="新建采集"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <!-- 采集类型 -->
      <el-form-item label="采集类型" prop="type">
        <el-radio-group v-model="form.type" @change="handleTypeChange">
          <el-radio :value="CollectionType.PRODUCT">商品链接</el-radio>
          <el-radio :value="CollectionType.STORE">店铺链接</el-radio>
          <el-radio :value="CollectionType.SEARCH">搜索</el-radio>
          <el-radio :value="CollectionType.OTHER">其他</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 链接输入框 (商品或店铺) -->
      <el-form-item
        v-if="form.type === CollectionType.PRODUCT || form.type === CollectionType.STORE"
        :label="form.type === CollectionType.PRODUCT ? '商品链接' : '店铺链接'"
        prop="links"
      >
        <el-input
          v-model="form.links"
          type="textarea"
          :rows="6"
          :placeholder="form.type === CollectionType.PRODUCT ? '请输入商品链接地址，一行一条' : '请输入店铺链接地址，一行一条'"
        />
        <div class="text-xs text-gray-500 mt-1">
          一行一条，目前仅支持亚马逊、Temu、Shein平台商品链接
        </div>
      </el-form-item>

      <!-- 搜索关键词 (搜索类型) -->
      <el-form-item
        v-if="form.type === CollectionType.SEARCH"
        label="搜索关键词"
        prop="keyword"
      >
        <el-input
          v-model="form.keyword"
          placeholder="请输入搜索关键词"
        />
      </el-form-item>

      <!-- 平台选择 (搜索类型) -->
      <el-form-item
        v-if="form.type === CollectionType.SEARCH"
        label="平台"
        prop="platform"
      >
        <el-radio-group v-model="form.platform">
          <el-radio :value="Platform.AMAZON">亚马逊</el-radio>
          <el-radio :value="Platform.TEMU">Temu</el-radio>
          <el-radio :value="Platform.SHEIN">Shein</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 下载采集器 (其他类型) -->
      <el-form-item v-if="form.type === CollectionType.OTHER" label="采集器">
        <el-button type="primary" @click="downloadCollector">
          <el-icon class="mr-1"><Download /></el-icon>
          下载采集器
        </el-button>
        <div class="text-xs text-gray-500 mt-1">
          请先下载并安装采集器，然后按照说明进行操作
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!canSubmit"
        >
          采集
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { createCollectionTask, CollectionType, Platform } from '../../../../store/product-collection';

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 导入枚举供模板使用
const { CollectionType, Platform } = { CollectionType, Platform };

// 响应式数据
const formRef = ref<FormInstance>();
const submitting = ref(false);

const form = ref({
  type: CollectionType.PRODUCT,
  links: '',
  keyword: '',
  platform: Platform.AMAZON
});

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const canSubmit = computed(() => {
  if (form.value.type === CollectionType.PRODUCT || form.value.type === CollectionType.STORE) {
    return form.value.links.trim() !== '';
  }
  if (form.value.type === CollectionType.SEARCH) {
    return form.value.keyword.trim() !== '' && form.value.platform !== '';
  }
  if (form.value.type === CollectionType.OTHER) {
    return true; // 其他类型只需要下载采集器
  }
  return false;
});

// 表单验证规则
const rules: FormRules = {
  type: [
    { required: true, message: '请选择采集类型', trigger: 'change' }
  ],
  links: [
    { required: true, message: '请输入链接地址', trigger: 'blur' }
  ],
  keyword: [
    { required: true, message: '请输入搜索关键词', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ]
};

// 方法
const handleTypeChange = () => {
  // 切换类型时清空相关字段
  form.value.links = '';
  form.value.keyword = '';
  form.value.platform = Platform.AMAZON;
};

const downloadCollector = () => {
  ElMessage.success('采集器下载功能开发中...');
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    // 调用API创建采集任务
    await createCollectionTask(form.value);

    ElMessage.success('采集任务创建成功！');
    emit('success');
    handleClose();
  } catch (error) {
    console.error('创建采集任务失败:', error);
    ElMessage.error('创建失败，请重试');
  } finally {
    submitting.value = false;
  }
};

const handleClose = () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.value = {
    type: CollectionType.PRODUCT,
    links: '',
    keyword: '',
    platform: Platform.AMAZON
  };
  emit('update:modelValue', false);
};

// 监听弹窗显示状态
watch(dialogVisible, (newVal) => {
  if (newVal) {
    // 弹窗打开时重置表单
    handleClose();
    emit('update:modelValue', true);
  }
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
