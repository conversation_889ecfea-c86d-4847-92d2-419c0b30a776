<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`采集详情 - ${collectionData?.id || ''}`"
    width="1000px"
    :before-close="handleClose"
  >
    <!-- 采集信息概览 -->
    <div v-if="collectionData" class="mb-6 p-4 bg-gray-50 rounded-lg">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="text-gray-500">采集类型：</span>
          <span class="font-medium">{{ collectionData.type }}</span>
        </div>
        <div>
          <span class="text-gray-500">平台：</span>
          <span class="font-medium">{{ collectionData.platform }}</span>
        </div>
        <div>
          <span class="text-gray-500">目标数量：</span>
          <span class="font-medium">{{ collectionData.targetCount }}</span>
        </div>
        <div>
          <span class="text-gray-500">成功数量：</span>
          <span class="font-medium text-green-600">{{ collectionData.successCount }}</span>
        </div>
      </div>
    </div>

    <!-- 采集结果列表 -->
    <div class="mb-4">
      <h3 class="text-lg font-medium mb-3">采集结果</h3>
      <el-table 
        :data="detailsList" 
        style="width: 100%" 
        v-loading="loading"
        max-height="400"
      >
        <el-table-column prop="index" label="序号" width="80" align="center" />
        <el-table-column label="主图" width="100" align="center">
          <template #default="scope">
            <div v-if="scope.row.mainImage" class="flex justify-center">
              <el-image
                :src="scope.row.mainImage"
                :preview-src-list="[scope.row.mainImage]"
                fit="cover"
                class="w-16 h-16 rounded border"
                :preview-teleported="true"
              />
            </div>
            <div v-else class="text-gray-400 text-xs">无图片</div>
          </template>
        </el-table-column>
        <el-table-column label="标题" min-width="200">
          <template #default="scope">
            <div v-if="scope.row.title" class="text-sm">
              {{ scope.row.title }}
            </div>
            <div v-else class="text-gray-400 text-xs">无标题</div>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="100" align="center">
          <template #default="scope">
            <span v-if="scope.row.price" class="text-red-600 font-medium">
              {{ scope.row.price }}
            </span>
            <span v-else class="text-gray-400 text-xs">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="rating" label="评分" width="100" align="center">
          <template #default="scope">
            <div v-if="scope.row.rating" class="flex items-center justify-center">
              <el-rate
                v-model="scope.row.rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
                size="small"
              />
            </div>
            <span v-else class="text-gray-400 text-xs">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.status === '成功' ? 'success' : 'danger'" 
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button 
              v-if="scope.row.originalUrl" 
              type="text" 
              size="small" 
              @click="openOriginalUrl(scope.row.originalUrl)"
            >
              查看原链接
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportDetails">
          <el-icon class="mr-1"><Download /></el-icon>
          导出详情
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';

// Props
interface Props {
  modelValue: boolean;
  collectionData: any;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 模拟详情数据
const detailsList = ref([
  {
    index: 1,
    title: 'Apple iPhone 15 Pro Max 256GB Natural Titanium',
    mainImage: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=200&h=200&fit=crop',
    price: '$1,199.00',
    rating: 4.5,
    status: '成功',
    originalUrl: 'https://amazon.com/example1'
  },
  {
    index: 2,
    title: 'Samsung Galaxy S24 Ultra 512GB Titanium Black',
    mainImage: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=200&h=200&fit=crop',
    price: '$1,299.99',
    rating: 4.3,
    status: '成功',
    originalUrl: 'https://amazon.com/example2'
  },
  {
    index: 3,
    title: '',
    mainImage: '',
    price: '',
    rating: 0,
    status: '失败',
    originalUrl: 'https://amazon.com/example3'
  },
  {
    index: 4,
    title: 'Google Pixel 8 Pro 128GB Obsidian',
    mainImage: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=200&h=200&fit=crop',
    price: '$999.00',
    rating: 4.2,
    status: '成功',
    originalUrl: 'https://amazon.com/example4'
  },
  {
    index: 5,
    title: 'OnePlus 12 256GB Flowy Emerald',
    mainImage: 'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?w=200&h=200&fit=crop',
    price: '$799.99',
    rating: 4.4,
    status: '成功',
    originalUrl: 'https://amazon.com/example5'
  }
]);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const openOriginalUrl = (url: string) => {
  window.open(url, '_blank');
};

const exportDetails = () => {
  ElMessage.success('导出详情功能开发中...');
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  loadDetails();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadDetails();
};

const loadDetails = () => {
  loading.value = true;
  // 模拟API调用
  setTimeout(() => {
    total.value = detailsList.value.length;
    loading.value = false;
  }, 300);
};

const handleClose = () => {
  emit('update:modelValue', false);
};

// 监听弹窗显示状态
watch(dialogVisible, (newVal) => {
  if (newVal && props.collectionData) {
    currentPage.value = 1;
    loadDetails();
  }
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
