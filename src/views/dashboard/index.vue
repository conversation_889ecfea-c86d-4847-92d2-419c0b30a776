<template>
  <div class="flex h-screen bg-gray-100">
    <!-- Sidebar -->
    <div class="w-64 bg-white shadow-md">
      <div class="p-4 text-2xl font-bold">RiinAi</div>
      <nav class="mt-10">
        <div v-for="item in menuItems" :key="item.name" class="mt-2">
          <!-- 一级菜单 -->
          <div v-if="!item.children" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200">
            <router-link :to="item.path" class="flex items-center w-full">
              <span class="mx-4">{{ item.name }}</span>
            </router-link>
          </div>

          <!-- 有子菜单的一级菜单 -->
          <div v-else>
            <div @click="toggleSubmenu(item.name)"
                 class="flex items-center justify-between px-4 py-2 text-gray-700 hover:bg-gray-200 cursor-pointer">
              <span class="mx-4">{{ item.name }}</span>
              <svg :class="{ 'rotate-180': expandedMenus.includes(item.name) }"
                   class="w-4 h-4 transition-transform duration-200"
                   fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>

            <!-- 二级菜单 -->
            <div v-show="expandedMenus.includes(item.name)" class="bg-gray-50">
              <router-link v-for="child in item.children" :key="child.name" :to="child.path"
                class="flex items-center px-8 py-2 text-sm text-gray-600 hover:bg-gray-200">
                <span>{{ child.name }}</span>
              </router-link>
            </div>
          </div>
        </div>
      </nav>
    </div>

    <!-- Main content -->
    <div class="flex flex-col flex-1">
      <!-- Header -->
      <header class="flex items-center justify-between p-4 bg-white shadow-md">
        <div></div>
        <div class="flex items-center">
          <span class="mr-4">欢迎, admin</span>
          <button @click="logout" class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            退出
          </button>
        </div>
      </header>

      <!-- Content -->
      <main class="flex-1 p-6">
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { isAuthenticated } from '../../store/auth';

const menuItems = ref([
  { name: '驾驶舱', path: '/dashboard' },
  {
    name: '我的应用',
    children: [
      { name: '商品采集', path: '/my-apps/product-collection' },
      { name: '智能裁图', path: '/my-apps/smart-crop' },
      { name: '一键抠图', path: '/my-apps/one-click-cutout' },
      { name: '超级裂变', path: '/my-apps/super-split' },
      { name: '标题生成', path: '/my-apps/title-generator' },
      { name: '批量刊登', path: '/my-apps/batch-listing' },
    ]
  },
  { name: '工作流', path: '/workflows' },
  { name: '图库管理', path: '/gallery' },
  { name: '商品管理', path: '/products' },
  { name: '应用市场', path: '/app-market' },
  { name: '账号设置', path: '/account-settings' },
]);

const expandedMenus = ref(['我的应用']); // 默认展开我的应用菜单

const toggleSubmenu = (menuName: string) => {
  const index = expandedMenus.value.indexOf(menuName);
  if (index > -1) {
    expandedMenus.value.splice(index, 1);
  } else {
    expandedMenus.value.push(menuName);
  }
};

const router = useRouter();

const logout = () => {
  localStorage.removeItem('user-token');
  isAuthenticated.value = false;
  router.push('/login');
};
</script>
