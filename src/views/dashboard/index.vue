<template>
  <div class="flex h-screen bg-gray-100">
    <!-- Sidebar -->
    <div class="w-64 bg-white shadow-md">
      <div class="p-4 text-2xl font-bold">RiinAi</div>
      <nav class="mt-10">
        <router-link v-for="item in menuItems" :key="item.name" :to="item.path"
          class="flex items-center px-4 py-2 mt-2 text-gray-700 hover:bg-gray-200">
          <span class="mx-4">{{ item.name }}</span>
        </a>
      </nav>
    </div>

    <!-- Main content -->
    <div class="flex flex-col flex-1">
      <!-- Header -->
      <header class="flex items-center justify-between p-4 bg-white shadow-md">
        <div></div>
        <div class="flex items-center">
          <span class="mr-4">欢迎, admin</span>
          <button @click="logout" class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            退出
          </button>
        </div>
      </header>

      <!-- Content -->
      <main class="flex-1 p-6">
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { isAuthenticated } from '../../store/auth';

const menuItems = ref([
  { name: '驾驶舱', path: '/dashboard' },
  { name: '我的应用', path: '/my-apps' },
  { name: '工作流', path: '/workflows' },
  { name: '图库管理', path: '/gallery' },
  { name: '商品管理', path: '/products' },
  { name: '应用市场', path: '/app-market' },
  { name: '账号设置', path: '/account-settings' },
]);

const router = useRouter();

const logout = () => {
  localStorage.removeItem('user-token');
  isAuthenticated.value = false;
  router.push('/login');
};
</script>
